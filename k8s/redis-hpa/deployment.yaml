apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-hpa
  namespace: redis-hpa
  labels:
    app: redis-hpa
    component: database
    version: "7.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-hpa
  template:
    metadata:
      labels:
        app: redis-hpa
        component: database
        version: "7.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "6379"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        fsGroup: 999
        runAsUser: 999
        runAsNonRoot: true
      containers:
      - name: redis
        image: redis:7.0-alpine
        ports:
        - containerPort: 6379
          name: redis
          protocol: TCP
        resources:
          requests:
            cpu: "2000m"     # 2 CPU para suportar HPA scaling
            memory: "4Gi"    # 4GB base
          limits:
            cpu: "6000m"     # 6 CPU máximo
            memory: "8Gi"    # 8GB máximo
        env:
        - name: REDIS_REPLICATION_MODE
          value: master
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /usr/local/etc/redis/redis.conf
          subPath: redis.conf
        command:
        - redis-server
        - /usr/local/etc/redis/redis.conf
        livenessProbe:
          tcpSocket:
            port: 6379
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 999
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-hpa-pvc
      - name: redis-config
        configMap:
          name: redis-hpa-config
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - redis-hpa
              topologyKey: kubernetes.io/hostname
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-hpa-insight
  namespace: redis-hpa
  labels:
    app: redis-hpa-insight
    component: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-hpa-insight
  template:
    metadata:
      labels:
        app: redis-hpa-insight
        component: monitoring
    spec:
      securityContext:
        fsGroup: 1001
        runAsUser: 1001
        runAsNonRoot: true
      containers:
      - name: redisinsight
        image: redislabs/redisinsight:latest
        ports:
        - containerPort: 5540
          name: http
          protocol: TCP
        resources:
          requests:
            cpu: "500m"
            memory: "512Mi"
          limits:
            cpu: "1000m"
            memory: "1Gi"
        volumeMounts:
        - name: redisinsight-data
          mountPath: /data
        env:
        - name: REDISINSIGHT_HOST
          value: "0.0.0.0"
        - name: REDISINSIGHT_PORT
          value: "5540"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 5540
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 5540
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 1001
      volumes:
      - name: redisinsight-data
        emptyDir: {}
