apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: conversas-ai-worker-queue-scaler
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: worker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-worker
  pollingInterval: 5                 
  cooldownPeriod: 180             
  minReplicaCount: 4                 
  maxReplicaCount: 40                
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300 
          policies:
          - type: Percent
            value: 20  
            periodSeconds: 180  
          - type: Pods
            value: 2   
            periodSeconds: 180
          selectPolicy: Min 
        scaleUp:
          stabilizationWindowSeconds: 30  
          policies:
          - type: Percent
            value: 100 
            periodSeconds: 30 
          - type: Pods
            value: 4   
            periodSeconds: 30
          selectPolicy: Max  
  triggers:
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"
      key: "actuator:common"                    # Mudança: usar chave JSON ao invés de lista
      targetValue: "5"                          # 1 worker para cada 5 mensagens na faixa "1 segundo a 5 minutos"
      activationTargetValue: "2"                # Ativa scaling com 2+ mensagens na faixa crítica
      query: "mensagens_a_processar_por_faixas[0].quantidade"  # JSONPath para quantidade da primeira faixa
  - type: cpu
    metadata:
      type: Utilization
      value: "75"                   
  - type: memory
    metadata:
      type: Utilization
      value: "90"  
