# HPA Integration com Actuator Common

## 🎯 Mudança Implementada

### **Antes: Fila Redis Simples**
```yaml
triggers:
- type: redis
  metadata:
    listName: delayed_messages
    listLength: "25"
    activationListLength: "10"
```

### **Depois: Chave JSON com Métricas Detalhadas**
```yaml
triggers:
- type: redis
  metadata:
    key: "actuator:common"
    targetValue: "5"
    activationTargetValue: "2"
    query: '$.mensagens_a_processar_por_faixas[?(@.faixa == "1 segundo a 5 minutos")].quantidade'
```

## 📊 Estrutura do JSON `actuator:common`

```json
{
    "name": "DelayedQueueWorker",
    "status": "",
    "uuid": "common",
    "mensagens_a_processar_agora": 0,
    "mensagens_a_processar_por_faixas": [
        {
            "faixa": "1 segundo a 5 minutos",
            "quantidade": 2                    ← USADO PARA SCALING
        },
        {
            "faixa": "5 a 15 minutos",
            "quantidade": 4
        },
        {
            "faixa": "15 a 30 minutos",
            "quantidade": 1
        },
        {
            "faixa": "30 a 60 minutos",
            "quantidade": 8
        },
        {
            "faixa": "1 hora a 1 dia",
            "quantidade": 0
        },
        {
            "faixa": "1 dia a +inf",
            "quantidade": 0
        }
    ]
}
```

## 🎯 Lógica de Scaling

### **Foco na Faixa Crítica:**
- **Faixa monitorada:** `"1 segundo a 5 minutos"`
- **Razão:** Mensagens que precisam ser processadas AGORA
- **Outras faixas:** Não são urgentes para scaling imediato

### **Thresholds:**
- **targetValue: "5"** → 1 worker para cada 5 mensagens críticas
- **activationTargetValue: "2"** → Ativa scaling com 2+ mensagens críticas

### **Cenários de Scaling:**

#### **Cenário 1: Poucas Mensagens Críticas**
```json
"quantidade": 1  // Faixa "1 segundo a 5 minutos"
```
- **Resultado:** Não escala (< 2 threshold)
- **Workers:** Mantém mínimo (4 workers)

#### **Cenário 2: Mensagens Críticas Moderadas**
```json
"quantidade": 8  // Faixa "1 segundo a 5 minutos"
```
- **Cálculo:** 8 ÷ 5 = 1.6 → 2 workers necessários
- **Resultado:** Escala para 6 workers (4 min + 2 extras)

#### **Cenário 3: Muitas Mensagens Críticas**
```json
"quantidade": 25  // Faixa "1 segundo a 5 minutos"
```
- **Cálculo:** 25 ÷ 5 = 5 workers necessários
- **Resultado:** Escala para 9 workers (4 min + 5 extras)

#### **Cenário 4: Pico Extremo**
```json
"quantidade": 100  // Faixa "1 segundo a 5 minutos"
```
- **Cálculo:** 100 ÷ 5 = 20 workers necessários
- **Resultado:** Escala para 24 workers (4 min + 20 extras)
- **Limite:** Máximo 40 workers

## 🔍 JSONPath Query Explicada

```javascript
'$.mensagens_a_processar_por_faixas[?(@.faixa == "1 segundo a 5 minutos")].quantidade'
```

### **Breakdown:**
- `$` → Raiz do JSON
- `.mensagens_a_processar_por_faixas` → Array de faixas
- `[?(@.faixa == "1 segundo a 5 minutos")]` → Filtra pela faixa específica
- `.quantidade` → Extrai o valor da quantidade

### **Resultado:**
- Retorna apenas o número de mensagens na faixa crítica
- Ignora outras faixas menos urgentes

## ⚡ Vantagens da Nova Abordagem

### **1. Scaling Mais Inteligente**
- ✅ Foca apenas em mensagens urgentes
- ✅ Ignora mensagens que podem esperar
- ✅ Scaling baseado em criticidade real

### **2. Melhor Performance**
- ✅ Não escala desnecessariamente para mensagens futuras
- ✅ Recursos concentrados no que importa
- ✅ Resposta mais rápida para urgências

### **3. Observabilidade Melhorada**
- ✅ Métricas detalhadas por faixa de tempo
- ✅ Visibilidade do status do DelayedQueueWorker
- ✅ Dados estruturados para monitoramento

### **4. Flexibilidade**
- ✅ Pode ajustar thresholds facilmente
- ✅ Pode mudar faixa monitorada se necessário
- ✅ Dados ricos para futuras otimizações

## 📊 Monitoramento

### **Verificar Dados do Actuator:**
```bash
# Conectar ao Redis
kubectl exec -it deployment/redis -n redis -- redis-cli

# Ver dados do actuator
GET actuator:common

# Verificar se JSON está válido
redis-cli -h redis-service.redis.svc.cluster.local GET actuator:common | jq .
```

### **Monitorar Scaling:**
```bash
# Ver status do HPA
kubectl describe scaledobject conversas-ai-worker-queue-scaler -n conversas-ai

# Ver workers atuais
kubectl get pods -n conversas-ai -l app=conversas-ai-worker

# Ver logs do KEDA
kubectl logs -n keda-system -l app=keda-operator
```

### **Testar JSONPath:**
```bash
# Testar query localmente
echo '{"mensagens_a_processar_por_faixas":[{"faixa":"1 segundo a 5 minutos","quantidade":10}]}' | \
jq '.mensagens_a_processar_por_faixas[] | select(.faixa == "1 segundo a 5 minutos") | .quantidade'
```

## 🔧 Troubleshooting

### **Problema: Scaling não funciona**
```bash
# 1. Verificar se chave existe
redis-cli -h redis-service.redis.svc.cluster.local EXISTS actuator:common

# 2. Verificar formato JSON
redis-cli -h redis-service.redis.svc.cluster.local GET actuator:common | jq .

# 3. Verificar logs KEDA
kubectl logs -n keda-system -l app=keda-operator | grep "conversas-ai-worker"
```

### **Problema: JSONPath não funciona**
- Verificar se a faixa existe exatamente como `"1 segundo a 5 minutos"`
- Testar JSONPath com dados reais
- Verificar se o JSON está bem formado

### **Problema: Scaling muito agressivo**
- Aumentar `targetValue` de 5 para 10
- Aumentar `activationTargetValue` de 2 para 5
- Ajustar `stabilizationWindowSeconds`

## 🎯 Configuração Final

```yaml
triggers:
- type: redis
  metadata:
    address: redis-service.redis.svc.cluster.local:6379
    databaseIndex: "0"
    key: "actuator:common"
    targetValue: "5"                          # 1 worker / 5 msgs críticas
    activationTargetValue: "2"                # Ativa com 2+ msgs críticas
    query: '$.mensagens_a_processar_por_faixas[?(@.faixa == "1 segundo a 5 minutos")].quantidade'
```

## 🎉 Resultado Esperado

Com esta configuração:

1. **Scaling mais preciso** baseado em urgência real
2. **Melhor utilização de recursos** (não escala para msgs futuras)
3. **Resposta mais rápida** para mensagens críticas
4. **Observabilidade rica** com métricas detalhadas por faixa
5. **Flexibilidade** para ajustes futuros baseados em dados reais

O HPA agora está integrado com o sistema de actuator e focará apenas nas mensagens que realmente precisam ser processadas imediatamente! 🚀
