apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: conversas-ai-worker
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: worker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-worker             
  minReplicaCount: 1                 
  maxReplicaCount: 40
  triggers:
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"
      key: "actuator:common"                    # Chave Redis com JSON do DelayedQueueWorker
      targetValue: "6"                          # 1 worker para cada 5 mensagens na faixa crítica
      activationTargetValue: "2"                # Ativa scaling com 2+ mensagens na faixa "1 segundo a 5 minutos"
      query: '$.mensagens_a_processar_por_faixas[?(@.faixa == "1 segundo a 5 minutos")].quantidade'  # JSONPath para faixa crítica
  - type: cpu
    metadata:
      type: Utilization
      value: "75"                   
  - type: memory
    metadata:
      type: Utilization
      value: "90"  
