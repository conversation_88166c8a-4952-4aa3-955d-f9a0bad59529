apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: conversas-ai-worker
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: worker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-worker
  pollingInterval: 5                  # Verifica a cada 5 segundos
  cooldownPeriod: 180                 # 3 minutos antes de reduzir
  minReplicaCount: 1                  # Mínimo de 4 workers
  maxReplicaCount: 40                 # Máximo de 40 workers
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300  # 5 min antes de reduzir
          policies:
          - type: Percent
            value: 20   # Reduz máximo 20% das réplicas por vez
            periodSeconds: 180
          - type: Pods
            value: 2    # Reduz máximo 2 pods por vez
            periodSeconds: 180
          selectPolicy: Min  # Usa a política mais conservadora
        scaleUp:
          stabilizationWindowSeconds: 30   # 30s antes de aumentar
          policies:
          - type: Percent
            value: 100  # Pode dobrar réplicas
            periodSeconds: 30
          - type: Pods
            value: 4    # Até 4 pods por vez
            periodSeconds: 30
          selectPolicy: Max  # Usa a política mais agressiva
  triggers:
  # Trigger alternativo para chave JSON (se KEDA suportar)
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"
      key: "actuator:common"
      targetValue: "6"
      activationTargetValue: "2"
  - type: cpu
    metadata:
      type: Utilization
      value: "75"                   
  - type: memory
    metadata:
      type: Utilization
      value: "90"  
