#!/bin/bash
# Configuração de autenticação para scripts do Grafana
# Gerado automaticamente em seg 18 ago 2025 11:13:28 -03

GRAFANA_URL="https://grafana.conversas.ai"
AUTH_METHOD="apikey"
AUTH_HEADER="Authorization: Bearer glsa_vlVDjsIGNj6tUoIgADnPiesyr84wZ7WY_3a12b4e5"

# Função para fazer chamadas autenticadas
grafana_auth_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    if [[ -n "$data" ]]; then
        curl -s -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$GRAFANA_URL/api/$endpoint"
    else
        curl -s -X "$method" \
            -H "$AUTH_HEADER" \
            -H "Content-Type: application/json" \
            "$GRAFANA_URL/api/$endpoint"
    fi
}
